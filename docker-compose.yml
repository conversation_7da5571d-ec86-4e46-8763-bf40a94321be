version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: k8s-manager-postgres
    environment:
      POSTGRES_DB: k8s_manager
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - k8s-manager-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: k8s-manager-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - k8s-manager-network

  # K8s Manager 应用
  k8s-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: k8s-manager-app
    ports:
      - "8080:8080"
    environment:
      - K8S_MANAGER_DATABASE_HOST=postgres
      - K8S_MANAGER_DATABASE_PORT=5432
      - K8S_MANAGER_DATABASE_USER=postgres
      - K8S_MANAGER_DATABASE_PASSWORD=postgres
      - K8S_MANAGER_DATABASE_DBNAME=k8s_manager
      - K8S_MANAGER_REDIS_HOST=redis
      - K8S_MANAGER_REDIS_PORT=6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./configs:/app/configs
      - ~/.kube:/root/.kube:ro  # 挂载 kubeconfig
    networks:
      - k8s-manager-network

volumes:
  postgres_data:
  redis_data:

networks:
  k8s-manager-network:
    driver: bridge
