# K8s Manager 配置文件

# 服务器配置
server:
  port: 8080
  mode: debug  # debug, release
  read_timeout: 60
  write_timeout: 60
  max_file_size: 33554432  # 32MB

# 数据库配置
database:
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  dbname: k8s_manager
  sslmode: disable
  max_idle_conns: 10
  max_open_conns: 100

# JWT 配置
jwt:
  secret: your-secret-key-change-in-production
  expire_time: 86400  # 24小时 (秒)
  issuer: k8s-manager

# 日志配置
log:
  level: info  # debug, info, warn, error
  format: text  # text, json
  output: stdout  # stdout 或文件路径

# Kubernetes 配置
k8s:
  default_kubeconfig: ""  # 默认 kubeconfig 路径
  clusters: {}  # 预配置的集群

# Redis 配置
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
