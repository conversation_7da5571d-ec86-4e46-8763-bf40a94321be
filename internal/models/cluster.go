package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Cluster 集群模型
type Cluster struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string         `json:"name" gorm:"uniqueIndex;not null"`
	DisplayName string         `json:"display_name"`
	Description string         `json:"description"`
	Provider    string         `json:"provider"`    // 云厂商: eks, ack, gke, on-prem
	Region      string         `json:"region"`      // 区域
	Version     string         `json:"version"`     // K8s版本
	Status      ClusterStatus  `json:"status"`      // 集群状态
	Config      string         `json:"config"`      // Kubeconfig内容(加密存储)
	APIServer   string         `json:"api_server"`  // API Server地址
	Token       string         `json:"token"`       // 访问令牌(加密存储)
	CertData    string         `json:"cert_data"`   // 证书数据(加密存储)
	Labels      string         `json:"labels"`      // 标签(JSON格式)
	CreatedBy   uuid.UUID      `json:"created_by"`  // 创建者ID
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Creator User `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// ClusterStatus 集群状态
type ClusterStatus int

const (
	ClusterStatusUnknown      ClusterStatus = 0 // 未知
	ClusterStatusConnecting   ClusterStatus = 1 // 连接中
	ClusterStatusHealthy      ClusterStatus = 2 // 健康
	ClusterStatusUnhealthy    ClusterStatus = 3 // 不健康
	ClusterStatusDisconnected ClusterStatus = 4 // 断开连接
	ClusterStatusMaintenance  ClusterStatus = 5 // 维护中
)

// AuditLog 审计日志模型
type AuditLog struct {
	ID         uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID     uuid.UUID      `json:"user_id" gorm:"type:uuid"`
	Username   string         `json:"username"`
	ClusterID  *uuid.UUID     `json:"cluster_id" gorm:"type:uuid"`
	Namespace  string         `json:"namespace"`
	Resource   string         `json:"resource"`     // 资源类型
	Action     string         `json:"action"`       // 操作类型
	Object     string         `json:"object"`       // 操作对象
	Result     string         `json:"result"`       // 操作结果
	Message    string         `json:"message"`      // 详细信息
	IP         string         `json:"ip"`           // 客户端IP
	UserAgent  string         `json:"user_agent"`   // 用户代理
	Duration   int64          `json:"duration"`     // 操作耗时(毫秒)
	CreatedAt  time.Time      `json:"created_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User    User     `json:"user" gorm:"foreignKey:UserID"`
	Cluster *Cluster `json:"cluster" gorm:"foreignKey:ClusterID"`
}

// DeploymentRecord 部署记录模型
type DeploymentRecord struct {
	ID          uuid.UUID         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ClusterID   uuid.UUID         `json:"cluster_id" gorm:"type:uuid;not null"`
	Namespace   string            `json:"namespace"`
	Name        string            `json:"name"`
	Type        string            `json:"type"`        // deployment, statefulset, daemonset
	Image       string            `json:"image"`       // 镜像
	Replicas    int32             `json:"replicas"`    // 副本数
	Status      DeploymentStatus  `json:"status"`      // 部署状态
	Strategy    string            `json:"strategy"`    // 部署策略
	Config      string            `json:"config"`      // 部署配置(YAML)
	Message     string            `json:"message"`     // 部署信息
	CreatedBy   uuid.UUID         `json:"created_by"`  // 创建者ID
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	DeletedAt   gorm.DeletedAt    `json:"-" gorm:"index"`

	// 关联关系
	Cluster Cluster `json:"cluster" gorm:"foreignKey:ClusterID"`
	Creator User    `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// DeploymentStatus 部署状态
type DeploymentStatus int

const (
	DeploymentStatusPending    DeploymentStatus = 0 // 等待中
	DeploymentStatusDeploying  DeploymentStatus = 1 // 部署中
	DeploymentStatusSucceeded  DeploymentStatus = 2 // 成功
	DeploymentStatusFailed     DeploymentStatus = 3 // 失败
	DeploymentStatusRollingOut DeploymentStatus = 4 // 滚动更新中
	DeploymentStatusRolledBack DeploymentStatus = 5 // 已回滚
)

// AlertRule 告警规则模型
type AlertRule struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string         `json:"name" gorm:"not null"`
	DisplayName string         `json:"display_name"`
	Description string         `json:"description"`
	ClusterID   *uuid.UUID     `json:"cluster_id" gorm:"type:uuid"`
	Namespace   string         `json:"namespace"`
	Resource    string         `json:"resource"`    // 监控资源
	Metric      string         `json:"metric"`      // 监控指标
	Condition   string         `json:"condition"`   // 告警条件
	Threshold   float64        `json:"threshold"`   // 阈值
	Duration    int            `json:"duration"`    // 持续时间(秒)
	Severity    AlertSeverity  `json:"severity"`    // 告警级别
	Enabled     bool           `json:"enabled"`     // 是否启用
	NotifyUsers []uuid.UUID    `json:"notify_users" gorm:"type:uuid[]"` // 通知用户
	CreatedBy   uuid.UUID      `json:"created_by"`  // 创建者ID
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Cluster *Cluster `json:"cluster" gorm:"foreignKey:ClusterID"`
	Creator User     `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// AlertSeverity 告警级别
type AlertSeverity int

const (
	AlertSeverityInfo     AlertSeverity = 1 // 信息
	AlertSeverityWarning  AlertSeverity = 2 // 警告
	AlertSeverityCritical AlertSeverity = 3 // 严重
	AlertSeverityFatal    AlertSeverity = 4 // 致命
)

// AlertRecord 告警记录模型
type AlertRecord struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	RuleID      uuid.UUID      `json:"rule_id" gorm:"type:uuid;not null"`
	ClusterID   *uuid.UUID     `json:"cluster_id" gorm:"type:uuid"`
	Namespace   string         `json:"namespace"`
	Resource    string         `json:"resource"`
	Message     string         `json:"message"`
	Value       float64        `json:"value"`       // 当前值
	Severity    AlertSeverity  `json:"severity"`    // 告警级别
	Status      AlertStatus    `json:"status"`      // 告警状态
	StartTime   time.Time      `json:"start_time"`  // 开始时间
	EndTime     *time.Time     `json:"end_time"`    // 结束时间
	AckBy       *uuid.UUID     `json:"ack_by" gorm:"type:uuid"` // 确认人
	AckTime     *time.Time     `json:"ack_time"`    // 确认时间
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Rule    AlertRule `json:"rule" gorm:"foreignKey:RuleID"`
	Cluster *Cluster  `json:"cluster" gorm:"foreignKey:ClusterID"`
	AckUser *User     `json:"ack_user" gorm:"foreignKey:AckBy"`
}

// AlertStatus 告警状态
type AlertStatus int

const (
	AlertStatusFiring   AlertStatus = 1 // 告警中
	AlertStatusResolved AlertStatus = 2 // 已解决
	AlertStatusAcked    AlertStatus = 3 // 已确认
)

// TableName 指定表名
func (Cluster) TableName() string {
	return "clusters"
}

func (AuditLog) TableName() string {
	return "audit_logs"
}

func (DeploymentRecord) TableName() string {
	return "deployment_records"
}

func (AlertRule) TableName() string {
	return "alert_rules"
}

func (AlertRecord) TableName() string {
	return "alert_records"
}

// BeforeCreate 创建前钩子
func (c *Cluster) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	return nil
}

func (a *AuditLog) BeforeCreate(tx *gorm.DB) error {
	if a.ID == uuid.Nil {
		a.ID = uuid.New()
	}
	return nil
}

func (d *DeploymentRecord) BeforeCreate(tx *gorm.DB) error {
	if d.ID == uuid.Nil {
		d.ID = uuid.New()
	}
	return nil
}

func (a *AlertRule) BeforeCreate(tx *gorm.DB) error {
	if a.ID == uuid.Nil {
		a.ID = uuid.New()
	}
	return nil
}

func (a *AlertRecord) BeforeCreate(tx *gorm.DB) error {
	if a.ID == uuid.Nil {
		a.ID = uuid.New()
	}
	return nil
}
