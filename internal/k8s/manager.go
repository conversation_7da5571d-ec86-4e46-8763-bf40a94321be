package k8s

import (
	"context"
	"fmt"
	"sync"

	"k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// Manager Kubernetes 客户端管理器
type Manager struct {
	clients map[string]*Client
	mutex   sync.RWMutex
}

// Client Kubernetes 客户端封装
type Client struct {
	Clientset  *kubernetes.Clientset
	RestConfig *rest.Config
	ClusterID  string
}

// NewManager 创建新的 Kubernetes 客户端管理器
func NewManager() *Manager {
	return &Manager{
		clients: make(map[string]*Client),
	}
}

// AddCluster 添加集群客户端
func (m *Manager) AddCluster(clusterID, kubeconfig string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 解析 kubeconfig
	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfig))
	if err != nil {
		return fmt.Errorf("failed to parse kubeconfig: %w", err)
	}

	// 创建 Kubernetes 客户端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("failed to create kubernetes client: %w", err)
	}

	// 测试连接
	ctx := context.Background()
	_, err = clientset.CoreV1().Namespaces().List(ctx, v1.ListOptions{Limit: 1})
	if err != nil {
		return fmt.Errorf("failed to connect to cluster: %w", err)
	}

	// 存储客户端
	m.clients[clusterID] = &Client{
		Clientset:  clientset,
		RestConfig: config,
		ClusterID:  clusterID,
	}

	return nil
}

// RemoveCluster 移除集群客户端
func (m *Manager) RemoveCluster(clusterID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	delete(m.clients, clusterID)
}

// GetClient 获取集群客户端
func (m *Manager) GetClient(clusterID string) (*Client, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	client, exists := m.clients[clusterID]
	if !exists {
		return nil, fmt.Errorf("cluster client not found: %s", clusterID)
	}

	return client, nil
}

// ListClusters 列出所有集群ID
func (m *Manager) ListClusters() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	clusters := make([]string, 0, len(m.clients))
	for clusterID := range m.clients {
		clusters = append(clusters, clusterID)
	}

	return clusters
}

// TestConnection 测试集群连接
func (m *Manager) TestConnection(clusterID string) error {
	client, err := m.GetClient(clusterID)
	if err != nil {
		return err
	}

	ctx := context.Background()
	_, err = client.Clientset.CoreV1().Namespaces().List(ctx, v1.ListOptions{Limit: 1})
	if err != nil {
		return fmt.Errorf("failed to connect to cluster %s: %w", clusterID, err)
	}

	return nil
}
