package api

import (
	"k8s-manager/internal/config"
	"k8s-manager/internal/k8s"
	"k8s-manager/internal/middleware"
	"k8s-manager/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Handler API 处理器
type Handler struct {
	db         *gorm.DB
	k8sManager *k8s.Manager
	config     *config.Config
	services   *services.Services
}

// NewHandler 创建新的 API 处理器
func NewHandler(db *gorm.DB, k8sManager *k8s.Manager, cfg *config.Config) *Handler {
	return &Handler{
		db:         db,
		k8sManager: k8sManager,
		config:     cfg,
		services:   services.NewServices(db, k8sManager, cfg),
	}
}

// SetupRoutes 设置路由
func (h *Handler) SetupRoutes(router *gin.Engine) {
	// 中间件
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())

	// API 版本分组
	v1 := router.Group("/api/v1")

	// 公开路由（不需要认证）
	public := v1.Group("/")
	{
		public.POST("/auth/login", h.Login)
		public.POST("/auth/register", h.Register)
		public.GET("/health", h.Health)
	}

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(middleware.JWTAuth(h.config.JWT.Secret))
	{
		// 用户管理
		users := protected.Group("/users")
		{
			users.GET("", h.ListUsers)
			users.POST("", h.CreateUser)
			users.GET("/:id", h.GetUser)
			users.PUT("/:id", h.UpdateUser)
			users.DELETE("/:id", h.DeleteUser)
			users.GET("/profile", h.GetProfile)
			users.PUT("/profile", h.UpdateProfile)
			users.PUT("/password", h.ChangePassword)
		}

		// 角色管理
		roles := protected.Group("/roles")
		{
			roles.GET("", h.ListRoles)
			roles.POST("", h.CreateRole)
			roles.GET("/:id", h.GetRole)
			roles.PUT("/:id", h.UpdateRole)
			roles.DELETE("/:id", h.DeleteRole)
		}

		// 权限管理
		permissions := protected.Group("/permissions")
		{
			permissions.GET("", h.ListPermissions)
			permissions.POST("", h.CreatePermission)
			permissions.GET("/:id", h.GetPermission)
			permissions.PUT("/:id", h.UpdatePermission)
			permissions.DELETE("/:id", h.DeletePermission)
		}

		// 集群管理
		clusters := protected.Group("/clusters")
		{
			clusters.GET("", h.ListClusters)
			clusters.POST("", h.CreateCluster)
			clusters.GET("/:id", h.GetCluster)
			clusters.PUT("/:id", h.UpdateCluster)
			clusters.DELETE("/:id", h.DeleteCluster)
			clusters.POST("/:id/test", h.TestClusterConnection)
			clusters.GET("/:id/nodes", h.GetClusterNodes)
			clusters.GET("/:id/status", h.GetClusterStatus)
		}

		// 命名空间管理
		namespaces := protected.Group("/clusters/:cluster_id/namespaces")
		{
			namespaces.GET("", h.ListNamespaces)
			namespaces.POST("", h.CreateNamespace)
			namespaces.GET("/:name", h.GetNamespace)
			namespaces.PUT("/:name", h.UpdateNamespace)
			namespaces.DELETE("/:name", h.DeleteNamespace)
		}

		// 工作负载管理
		workloads := protected.Group("/clusters/:cluster_id/namespaces/:namespace")
		{
			// Deployments
			deployments := workloads.Group("/deployments")
			{
				deployments.GET("", h.ListDeployments)
				deployments.POST("", h.CreateDeployment)
				deployments.GET("/:name", h.GetDeployment)
				deployments.PUT("/:name", h.UpdateDeployment)
				deployments.DELETE("/:name", h.DeleteDeployment)
				deployments.POST("/:name/scale", h.ScaleDeployment)
				deployments.POST("/:name/restart", h.RestartDeployment)
				deployments.POST("/:name/rollback", h.RollbackDeployment)
			}

			// StatefulSets
			statefulsets := workloads.Group("/statefulsets")
			{
				statefulsets.GET("", h.ListStatefulSets)
				statefulsets.POST("", h.CreateStatefulSet)
				statefulsets.GET("/:name", h.GetStatefulSet)
				statefulsets.PUT("/:name", h.UpdateStatefulSet)
				statefulsets.DELETE("/:name", h.DeleteStatefulSet)
			}

			// DaemonSets
			daemonsets := workloads.Group("/daemonsets")
			{
				daemonsets.GET("", h.ListDaemonSets)
				daemonsets.POST("", h.CreateDaemonSet)
				daemonsets.GET("/:name", h.GetDaemonSet)
				daemonsets.PUT("/:name", h.UpdateDaemonSet)
				daemonsets.DELETE("/:name", h.DeleteDaemonSet)
			}

			// Pods
			pods := workloads.Group("/pods")
			{
				pods.GET("", h.ListPods)
				pods.GET("/:name", h.GetPod)
				pods.DELETE("/:name", h.DeletePod)
				pods.GET("/:name/logs", h.GetPodLogs)
				pods.GET("/:name/events", h.GetPodEvents)
				pods.POST("/:name/exec", h.ExecPod)
			}

			// Services
			services := workloads.Group("/services")
			{
				services.GET("", h.ListServices)
				services.POST("", h.CreateService)
				services.GET("/:name", h.GetService)
				services.PUT("/:name", h.UpdateService)
				services.DELETE("/:name", h.DeleteService)
			}

			// ConfigMaps
			configmaps := workloads.Group("/configmaps")
			{
				configmaps.GET("", h.ListConfigMaps)
				configmaps.POST("", h.CreateConfigMap)
				configmaps.GET("/:name", h.GetConfigMap)
				configmaps.PUT("/:name", h.UpdateConfigMap)
				configmaps.DELETE("/:name", h.DeleteConfigMap)
			}

			// Secrets
			secrets := workloads.Group("/secrets")
			{
				secrets.GET("", h.ListSecrets)
				secrets.POST("", h.CreateSecret)
				secrets.GET("/:name", h.GetSecret)
				secrets.PUT("/:name", h.UpdateSecret)
				secrets.DELETE("/:name", h.DeleteSecret)
			}
		}

		// 监控和日志
		monitoring := protected.Group("/monitoring")
		{
			monitoring.GET("/clusters/:cluster_id/metrics", h.GetClusterMetrics)
			monitoring.GET("/clusters/:cluster_id/nodes/:node/metrics", h.GetNodeMetrics)
			monitoring.GET("/clusters/:cluster_id/namespaces/:namespace/pods/:pod/metrics", h.GetPodMetrics)
		}

		// 审计日志
		audit := protected.Group("/audit")
		{
			audit.GET("/logs", h.ListAuditLogs)
			audit.GET("/logs/:id", h.GetAuditLog)
		}

		// 告警管理
		alerts := protected.Group("/alerts")
		{
			alerts.GET("/rules", h.ListAlertRules)
			alerts.POST("/rules", h.CreateAlertRule)
			alerts.GET("/rules/:id", h.GetAlertRule)
			alerts.PUT("/rules/:id", h.UpdateAlertRule)
			alerts.DELETE("/rules/:id", h.DeleteAlertRule)
			alerts.GET("/records", h.ListAlertRecords)
			alerts.POST("/records/:id/ack", h.AckAlertRecord)
		}

		// WebSocket 路由
		protected.GET("/ws/terminal/:cluster_id/:namespace/:pod/:container", h.HandleWebSocketTerminal)
		protected.GET("/ws/logs/:cluster_id/:namespace/:pod/:container", h.HandleWebSocketLogs)
	}

	// Swagger 文档
	// router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}
