package services

import (
	"k8s-manager/internal/config"
	"k8s-manager/internal/k8s"

	"gorm.io/gorm"
)

// Services 服务集合
type Services struct {
	Auth       *AuthService
	User       *UserService
	Cluster    *ClusterService
	Namespace  *NamespaceService
	Workload   *WorkloadService
	Monitoring *MonitoringService
	Audit      *AuditService
	Alert      *AlertService
}

// NewServices 创建服务集合
func NewServices(db *gorm.DB, k8sManager *k8s.Manager, cfg *config.Config) *Services {
	return &Services{
		Auth:       NewAuthService(db, cfg),
		User:       NewUserService(db),
		Cluster:    NewClusterService(db, k8sManager),
		Namespace:  NewNamespaceService(k8sManager),
		Workload:   NewWorkloadService(k8sManager),
		Monitoring: NewMonitoringService(k8sManager),
		Audit:      NewAuditService(db),
		Alert:      NewAlertService(db, k8sManager),
	}
}
