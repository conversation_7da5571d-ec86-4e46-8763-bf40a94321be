package services

import (
	"errors"

	"k8s-manager/internal/models"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{db: db}
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Nickname string `json:"nickname"`
	Status   models.UserStatus `json:"status"`
	RoleIDs  []uuid.UUID `json:"role_ids"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string `json:"username"`
	Email    string `json:"email" binding:"omitempty,email"`
	Nickname string `json:"nickname"`
	Status   *models.UserStatus `json:"status"`
	RoleIDs  []uuid.UUID `json:"role_ids"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// ListUsersRequest 用户列表请求
type ListUsersRequest struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=10"`
	Keyword  string `form:"keyword"`
	Status   *models.UserStatus `form:"status"`
}

// ListUsersResponse 用户列表响应
type ListUsersResponse struct {
	Users []models.User `json:"users"`
	Total int64         `json:"total"`
	Page  int           `json:"page"`
	PageSize int        `json:"page_size"`
}

// ListUsers 获取用户列表
func (s *UserService) ListUsers(req *ListUsersRequest) (*ListUsersResponse, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{}).Preload("Roles")

	// 关键字搜索
	if req.Keyword != "" {
		query = query.Where("username ILIKE ? OR email ILIKE ? OR nickname ILIKE ?", 
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 状态过滤
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Find(&users).Error; err != nil {
		return nil, err
	}

	// 清除密码字段
	for i := range users {
		users[i].Password = ""
	}

	return &ListUsersResponse{
		Users:    users,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetUser 获取用户详情
func (s *UserService) GetUser(id uuid.UUID) (*models.User, error) {
	var user models.User
	err := s.db.Preload("Roles.Permissions").First(&user, id).Error
	if err != nil {
		return nil, err
	}

	// 清除密码字段
	user.Password = ""

	return &user, nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(req *CreateUserRequest) (*models.User, error) {
	// 检查用户名是否已存在
	var count int64
	s.db.Model(&models.User{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	s.db.Model(&models.User{}).Where("email = ?", req.Email).Count(&count)
	if count > 0 {
		return nil, errors.New("邮箱已存在")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: string(hashedPassword),
		Nickname: req.Nickname,
		Status:   req.Status,
	}

	if user.Status == 0 {
		user.Status = models.UserStatusActive
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建用户
	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 分配角色
	if len(req.RoleIDs) > 0 {
		for _, roleID := range req.RoleIDs {
			userRole := models.UserRole{
				UserID: user.ID,
				RoleID: roleID,
			}
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 重新加载用户数据
	s.db.Preload("Roles").First(&user, user.ID)

	// 清除密码字段
	user.Password = ""

	return &user, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(id uuid.UUID, req *UpdateUserRequest) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		return nil, err
	}

	// 检查用户名是否已存在（排除当前用户）
	if req.Username != "" && req.Username != user.Username {
		var count int64
		s.db.Model(&models.User{}).Where("username = ? AND id != ?", req.Username, id).Count(&count)
		if count > 0 {
			return nil, errors.New("用户名已存在")
		}
		user.Username = req.Username
	}

	// 检查邮箱是否已存在（排除当前用户）
	if req.Email != "" && req.Email != user.Email {
		var count int64
		s.db.Model(&models.User{}).Where("email = ? AND id != ?", req.Email, id).Count(&count)
		if count > 0 {
			return nil, errors.New("邮箱已存在")
		}
		user.Email = req.Email
	}

	// 更新其他字段
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.Status != nil {
		user.Status = *req.Status
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新用户
	if err := tx.Save(&user).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 更新角色关联
	if req.RoleIDs != nil {
		// 删除现有角色关联
		if err := tx.Where("user_id = ?", id).Delete(&models.UserRole{}).Error; err != nil {
			tx.Rollback()
			return nil, err
		}

		// 添加新的角色关联
		for _, roleID := range req.RoleIDs {
			userRole := models.UserRole{
				UserID: user.ID,
				RoleID: roleID,
			}
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 重新加载用户数据
	s.db.Preload("Roles").First(&user, user.ID)

	// 清除密码字段
	user.Password = ""

	return &user, nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(id uuid.UUID) error {
	return s.db.Delete(&models.User{}, id).Error
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(id uuid.UUID, req *ChangePasswordRequest) error {
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword)); err != nil {
		return errors.New("旧密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	user.Password = string(hashedPassword)
	return s.db.Save(&user).Error
}
